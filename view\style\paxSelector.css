/* ==================== 人数选择器组件样式 ==================== */

/* 基于主题色的自适应设计变量 */
:root {
  --pax-theme-primary: var(--styleColor, #007bff);
  --pax-theme-light: color-mix(in srgb, var(--pax-theme-primary) 15%, white);
  --pax-theme-lighter: color-mix(in srgb, var(--pax-theme-primary) 8%, white);
  --pax-theme-dark: color-mix(in srgb, var(--pax-theme-primary) 80%, black);
  --pax-theme-alpha-10: color-mix(in srgb, var(--pax-theme-primary) 10%, transparent);
  --pax-theme-alpha-20: color-mix(in srgb, var(--pax-theme-primary) 20%, transparent);
  --pax-theme-alpha-30: color-mix(in srgb, var(--pax-theme-primary) 30%, transparent);

  --pax-glass-bg: rgba(255, 255, 255, 0.92);
  --pax-glass-border: rgba(255, 255, 255, 0.3);
  --pax-shadow-soft: 0 4px 20px rgba(0, 0, 0, 0.1);
  --pax-shadow-medium: 0 6px 25px rgba(0, 0, 0, 0.08);
  --pax-shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.06);
  --pax-text-primary: #2d3748;
  --pax-text-secondary: #4a5568;
  --pax-text-light: #718096;
  --pax-border-color: #e2e8f0;
}

.pax-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.6) 100%);
  backdrop-filter: blur(6px);
  -webkit-backdrop-filter: blur(6px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.pax-selector-container {
  background: var(--pax-glass-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--pax-glass-border);
  border-radius: 0.75rem;
  box-shadow: var(--pax-shadow-soft);
  width: 75%;
  max-width: 16rem;
  overflow: hidden;
  animation: paxSelectorSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

.pax-selector-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--pax-theme-primary);
  border-radius: 0.75rem 0.75rem 0 0;
}

@keyframes paxSelectorSlideIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.pax-selector-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 0.75rem 0.4rem;
  background: linear-gradient(135deg, var(--pax-theme-alpha-10) 0%, transparent 100%);
}

.pax-selector-title {
  margin: 0;
  font-size: 0.4rem;
  font-weight: 600;
  color: var(--pax-text-primary);
  text-align: center;
  letter-spacing: 0.01em;
}

.pax-selector-content {
  padding: 0 0.75rem 0.5rem;
}

.pax-number-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 0.25rem;
  margin-bottom: 0.35rem;
}

.pax-number-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid var(--pax-border-color);
  border-radius: 0.375rem;
  padding: 0.2rem 0.15rem;
  font-size: 0.375rem;
  font-weight: 500;
  color: var(--pax-text-secondary);
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  height: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  box-shadow: var(--pax-shadow-subtle);
  position: relative;
  overflow: hidden;
}

.pax-number-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--pax-theme-light);
  opacity: 0;
  transition: opacity 0.25s ease;
}

.pax-number-btn span {
  position: relative;
  z-index: 1;
}

.pax-number-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px var(--pax-theme-alpha-20);
  border-color: var(--pax-theme-alpha-30);
}

.pax-number-btn:hover::before {
  opacity: 1;
}

.pax-number-btn.empty {
  background: transparent;
  border: none;
  cursor: default;
  pointer-events: none;
  box-shadow: none;
}

.pax-number-btn.other {
  font-size: 0.3rem;
  background: var(--pax-theme-lighter);
  color: var(--pax-theme-dark);
  border-color: var(--pax-theme-alpha-20);
}

.pax-number-btn.other:hover {
  box-shadow: 0 4px 15px var(--pax-theme-alpha-20);
}

.pax-number-btn.selected {
  background: var(--pax-theme-primary);
  color: white;
  border-color: transparent;
  transform: translateY(-2px) scale(1.03);
  box-shadow: 0 6px 20px var(--pax-theme-alpha-30);
  font-weight: 600;
}

.pax-number-btn.selected::before {
  opacity: 0;
}

.pax-other-input-wrapper {
  margin-top: 0.25rem;
  animation: paxInputSlideDown 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes paxInputSlideDown {
  from {
    opacity: 0;
    max-height: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    max-height: 1.5rem;
    transform: translateY(0);
  }
}

.pax-other-input {
  width: 100%;
  padding: 0.25rem 0.375rem;
  border: 1.5px solid var(--pax-border-color);
  border-radius: 0.375rem;
  font-size: 0.35rem;
  color: var(--pax-text-primary);
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.pax-other-input:focus {
  outline: none;
  border-color: var(--pax-theme-primary);
  box-shadow: 0 0 0 2px var(--pax-theme-alpha-20), inset 0 1px 3px rgba(0, 0, 0, 0.05);
  background: white;
  transform: scale(1.01);
}

.pax-other-input::placeholder {
  color: var(--pax-text-light);
  font-style: italic;
}

.pax-selector-footer {
  padding: 0 0.75rem 0.75rem;
}

.pax-confirm-btn {
  width: 100%;
  background: var(--pax-theme-primary);
  border: none;
  border-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.4rem;
  font-weight: 600;
  color: white;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 12px var(--pax-theme-alpha-30);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.pax-confirm-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.4s ease;
}

.pax-confirm-btn:hover {
  transform: translateY(-1px) scale(1.01);
  box-shadow: 0 6px 20px var(--pax-theme-alpha-30);
  background: var(--pax-theme-dark);
}

.pax-confirm-btn:hover::before {
  left: 100%;
}

.pax-confirm-btn.animate {
  animation: buttonPulse 2s ease-in-out infinite, buttonGlow 2.5s ease-in-out infinite alternate;
}

@keyframes buttonPulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes buttonGlow {
  0% {
    box-shadow: 0 3px 12px var(--pax-theme-alpha-30);
  }
  100% {
    box-shadow: 0 5px 20px var(--pax-theme-alpha-30);
  }
}

.pax-confirm-btn:active:not(.disabled) {
  transform: translateY(0) scale(0.98);
  transition: transform 0.1s ease;
}

.pax-confirm-btn.disabled {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
  color: var(--pax-text-light);
  cursor: not-allowed;
  animation: none;
  transform: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  text-shadow: none;
}

.pax-confirm-btn.disabled::before {
  display: none;
}

/* 只为iPhone 6/7/8优化 */
@media only screen and (device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) {
  .pax-selector-container {
    width: 70%;
    max-width: 15rem;
  }

  .pax-selector-header {
    padding: 0.5rem 0.6rem 0.3rem;
  }

  .pax-selector-title {
    font-size: 0.375rem;
  }

  .pax-selector-content {
    padding: 0 0.6rem 0.4rem;
  }

  .pax-number-grid {
    gap: 0.2rem;
    margin-bottom: 0.3rem;
  }

  .pax-number-btn {
    padding: 0.15rem 0.1rem;
    font-size: 0.32rem;
    height: 0.7rem;
    border-radius: 0.3rem;
  }

  .pax-other-input {
    padding: 0.2rem 0.3rem;
    font-size: 0.3rem;
    border-radius: 0.3rem;
  }

  .pax-confirm-btn {
    padding: 0.4rem 0.6rem;
    font-size: 0.35rem;
    border-radius: 0.4rem;
  }

  .pax-selector-footer {
    padding: 0 0.6rem 0.6rem;
  }
}
